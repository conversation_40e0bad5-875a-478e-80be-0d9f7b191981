<template>
  <div id="ai-chat-container">
    <chat-button @click="toggleChat" :position="config.position" :offsetX="config.offsetX || 20"
      :offsetY="config.offsetY || 20" />
    <chat-window v-if="(config.openMode || 'iframe') === 'iframe'" ref="chatWindow" :isOpen="isOpen"
      :config="windowConfig" @close="toggleChat(false)" />
  </div>
</template>

<script>
import ChatButton from './ChatButton.vue';
import ChatWindow from './ChatWindow.vue';

export default {
  name: 'ChatApp',
  components: {
    ChatButton,
    ChatWindow
  },
  props: {
    config: {
      type: Object,
      default: () => ({
        position: 'bottom-right',
        offsetX: 20,
        offsetY: 20,
        openMode: 'iframe' // 默认使用 iframe 模式
      })
    }
  },
  data() {
    return {
      isOpen: false,
      finalUrl: '' // 存储构建好的完整 URL
    };
  },
  computed: {
    // 为 ChatWindow 准备的配置，包含构建好的完整 URL
    windowConfig() {
      return {
        ...this.config,
        url: this.finalUrl || this.config.url
      };
    }
  },
  methods: {
    toggleChat(show = !this.isOpen) {
      // 根据配置的 openMode 决定打开方式
      const openMode = this.config.openMode || 'iframe'; // 向后兼容，默认 iframe 模式

      if (openMode === 'newTab') {
      // newTab 模式：每次点击都打开新标签页（不管当前状态）
        if (this.config.url) {
          // 构建带有 ck 参数的完整 URL
          const finalUrl = this.buildUrlWithCookie(this.config.url);
          window.open(finalUrl, '_blank');
          // 触发打开回调
          if (typeof this.config.onOpen === 'function') {
            this.config.onOpen();
          }
        } else {
          console.warn('AI Chat SDK: 在 newTab 模式下需要配置 url 参数');
        }
        return; // newTab 模式下不需要切换 iframe 窗口状态
      }

      // iframe 模式：使用原有的弹窗逻辑
      if (show) {
        // 打开弹窗时构建完整 URL
        this.finalUrl = this.buildUrlWithCookie(this.config.url);
      } else {
        // 关闭弹窗时清空 URL
        this.finalUrl = '';
      }

      this.isOpen = show;

      // 触发回调（如果存在）
      if (this.isOpen && typeof this.config.onOpen === 'function') {
        this.config.onOpen();
      } else if (!this.isOpen && typeof this.config.onClose === 'function') {
        this.config.onClose();
      }
    },
    openChat() {
      this.toggleChat(true);
    },
    closeChat() {
      this.toggleChat(false);
    },

    /**
     * 构建带有 cookie 参数的 URL
     * @param {string} baseUrl - 原始 URL
     * @returns {string} 拼接后的 URL
     */
    buildUrlWithCookie(baseUrl) {
      if (!baseUrl) return '';

      // 获取 Authorization cookie
      const authCookie = this.getCookie('Authorization');
      if (!authCookie) {
        console.log('未找到 Authorization cookie，使用原始 URL');
        return baseUrl;
      }

      // 构建带有 ck 参数的 URL
      const finalUrl = this.appendCookieToUrl(baseUrl, authCookie);
      console.log('构建的 URL：', finalUrl);
      return finalUrl;
    },

    /**
     * 获取指定名称的 cookie 值
     * @param {string} name - cookie 名称
     * @returns {string|null} cookie 值，如果不存在则返回 null
     */
    getCookie(name) {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) {
        return parts.pop().split(';').shift();
      }
      return null;
    },

    /**
     * 将 cookie 值作为 ck 参数添加到 URL 中
     * @param {string} url - 原始 URL
     * @param {string} cookieValue - cookie 值
     * @returns {string} 拼接后的 URL
     */
    appendCookieToUrl(url, cookieValue) {
      try {
        const urlObj = new URL(url);
        urlObj.searchParams.set('ck', cookieValue);
        return urlObj.toString();
      } catch (error) {
        // 如果 URL 格式不正确，使用简单的字符串拼接
        console.warn('URL 格式不正确，使用简单拼接方式：', error);
        const separator = url.includes('?') ? '&' : '?';
        return `${url}${separator}ck=${encodeURIComponent(cookieValue)}`;
      }
    }
  }
}
</script>

<style scoped>
#ai-chat-container {
  font-family: Arial, sans-serif;
  line-height: 1.5;
  color: #333;
}
</style> 